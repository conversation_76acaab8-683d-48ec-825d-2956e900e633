<template>
  <page-wrapper v-loading="loading" @back="go(-1)" title="固件详情">
    <template #headerContent>
      <BasicUser :username="get(apiResult, 'name')">
        <template #extra>
          <div class="text-sm text-gray-500"> 版本: {{ get(apiResult, 'version') }} </div>
        </template>
        <a-tag color="blue">{{ get(apiResult, 'signType', 'MD5') }}</a-tag>
      </BasicUser>
    </template>

    <div class="mt-4">
      <a-descriptions title="基本信息" :column="2" bordered>
        <a-descriptions-item label="固件名称">
          {{ get(apiResult, 'name', '-') }}
        </a-descriptions-item>
        <a-descriptions-item label="固件版本">
          {{ get(apiResult, 'version', '-') }}
        </a-descriptions-item>
        <a-descriptions-item label="签名方式">
          {{ get(apiResult, 'signType', '-') }}
        </a-descriptions-item>
        <a-descriptions-item label="签名值">
          {{ get(apiResult, 'sign', '-') }}
        </a-descriptions-item>
        <a-descriptions-item label="文件地址" :span="2">
          <a-button v-if="get(apiResult, 'file')" type="link" size="small" @click="downloadFile">
            {{ get(apiResult, 'file') }}
          </a-button>
          <span v-else>-</span>
        </a-descriptions-item>
        <a-descriptions-item label="支持产品" :span="2">
          <a-space v-if="get(apiResult, 'productList', []).length > 0">
            <a-tag
              v-for="product in get(apiResult, 'productList', [])"
              :key="product.id"
              color="green"
            >
              {{ product.model }}
            </a-tag>
          </a-space>
          <span v-else>-</span>
        </a-descriptions-item>
        <a-descriptions-item label="固件描述" :span="2">
          {{ get(apiResult, 'remark', '-') }}
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { useGo } from '@/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import BasicUser from '@/components/Custom/BasicUser.vue';
  import { get } from 'lodash-es';
  import { message } from 'ant-design-vue';

  const go = useGo();
  const route = useRoute();
  const params = route.params;

  // 这里应该调用获取固件详情的API，目前使用模拟数据
  const {
    reload: _reload,
    loading,
    apiResult,
  } = useApiLoading({
    api: async () => {
      // TODO: 实现获取固件详情的API调用
      // 例如: return apiFirmwareStoreDetail(params.id);
      return {
        id: params.id,
        name: 'Indoor Firmware',
        version: 'V2.0',
        signType: 'MD5',
        sign: 'abc123def456',
        file: '/path/to/firmware.bin',
        remark: '修复一些已知问题',
        productList: [
          { id: 1, model: 'FH30112' },
          { id: 2, model: 'FH512050' },
        ],
      };
    },
    params,
  });

  // 下载固件文件
  const downloadFile = () => {
    const fileUrl = get(apiResult.value, 'file');
    if (fileUrl) {
      window.open(fileUrl, '_blank');
    } else {
      message.warning('文件地址不存在');
    }
  };
</script>

<style lang="less" scoped></style>
